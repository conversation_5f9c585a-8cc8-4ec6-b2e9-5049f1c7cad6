#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Ghostscript PCLM转换功能
"""

import sys
import logging
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from print_sniffer_pcap import EnhancedPrintDataProcessor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_ghostscript_path():
    """测试Ghostscript路径检测"""
    print("=== 测试Ghostscript路径检测 ===")
    processor = EnhancedPrintDataProcessor()
    gs_path = processor._get_ghostscript_path()
    
    if gs_path:
        print(f"✅ 找到Ghostscript: {gs_path}")
        return True
    else:
        print("❌ 未找到Ghostscript")
        return False

def test_pclm_processing():
    """测试PCLM处理功能"""
    print("\n=== 测试PCLM处理功能 ===")
    processor = EnhancedPrintDataProcessor()
    
    # 创建一个简单的测试PCLM数据
    test_pclm_data = b"""
%PDF-1.4
% genPCLm 1.0
% strip-height: 128
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
>>
endobj

xref
0 4
0000000000 65535 f 
0000000009 00000 n 
0000000074 00000 n 
0000000120 00000 n 
trailer
<<
/Size 4
/Root 1 0 R
>>
startxref
199
%%EOF
"""
    
    try:
        result = processor.process_pclm_data(test_pclm_data)
        if result and result.startswith(b'%PDF'):
            print("✅ PCLM处理成功")
            print(f"   输入大小: {len(test_pclm_data)} 字节")
            print(f"   输出大小: {len(result)} 字节")
            return True
        else:
            print("❌ PCLM处理失败")
            return False
    except Exception as e:
        print(f"❌ PCLM处理出错: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试Ghostscript PCLM转换功能...\n")
    
    # 测试Ghostscript路径
    gs_available = test_ghostscript_path()
    
    # 测试PCLM处理
    pclm_success = test_pclm_processing()
    
    print("\n=== 测试结果总结 ===")
    print(f"Ghostscript可用性: {'✅' if gs_available else '❌'}")
    print(f"PCLM处理功能: {'✅' if pclm_success else '❌'}")
    
    if gs_available and pclm_success:
        print("\n🎉 所有测试通过！Ghostscript PCLM转换功能正常工作。")
        return 0
    elif pclm_success:
        print("\n⚠️  PCLM处理功能正常，但使用的是回退方法（未找到Ghostscript）。")
        return 0
    else:
        print("\n❌ 测试失败，请检查配置。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
