# 打印流量嗅探器 - 详细代码功能说明文档

## 📋 项目概述

本项目是一个功能完整的Python打印数据捕获和流量监听系统，能够监听本机发送到打印机的网络流量，捕获各种格式的打印数据，并将其还原为相应的文件格式。项目包含两种主要工作模式：TCP服务器模式和网络流量嗅探模式。

## 🗂️ 文件结构与功能

### 核心Python文件

#### 1. `print_sniffer_pcap.py` (1012行)
**主要功能：基于Scapy的网络流量嗅探器**

**核心类和方法：**

- **`EnhancedPrintDataProcessor`类**
  - `__init__(output_dir)`: 初始化数据处理器，设置输出目录
  - `detect_format(data)`: 智能检测数据格式（PDF、PostScript、PCLm、PNG、JPEG等）
  - `_get_ghostscript_path()`: 检测并获取Ghostscript可执行文件路径
  - `_convert_pclm_with_ghostscript(pclm_data)`: 使用Ghostscript转换PCLm数据为PDF
  - `_is_http_wrapped_pclm(data)`: 检测HTTP/SOAP包装的PCLm数据
  - `_extract_pclm_from_http(data)`: 从HTTP包装中提取纯PCLm数据
  - `process_pclm_data(data)`: 处理PCLm数据，支持Ghostscript转换
  - `generate_filename(format_type, client_addr)`: 生成带时间戳的文件名
  - `save_data(data, client_id)`: 保存数据到文件

- **`PrintSniffer`类**
  - `__init__(target_ports, enable_wsd, max_stream_size)`: 初始化嗅探器
  - `is_print_packet(packet)`: 判断是否为打印相关数据包
  - `get_stream_key(packet)`: 获取TCP流的唯一标识
  - `process_packet(packet)`: 处理捕获的数据包
  - `_is_data_complete(stream_key)`: 检测数据是否完整
  - `finalize_stream(stream_key)`: 完成TCP流重组并保存数据
  - `cleanup_old_streams()`: 清理超时的TCP流
  - `start_sniffing(interface)`: 开始嗅探网络流量
  - `stop_sniffing()`: 停止嗅探

**支持的协议端口：**
- 9100 (AppSocket/JetDirect)
- 515 (LPD)
- 631 (IPP)
- 445/139 (SMB/CIFS)
- 3910, 9220, 9500, 9600 (厂商特定端口)

#### 2. `test_data_completeness.py` (117行)
**功能：测试数据完整性检测功能**

**主要测试：**
- 完整PDF数据检测
- 不完整PDF数据检测
- SOAP数据完整性检测
- HTTP Content-Length检测

#### 3. `test_ghostscript_conversion.py` (124行)
**功能：测试Ghostscript PCLm转换功能**

**主要功能：**
- `test_ghostscript_path()`: 测试Ghostscript路径检测
- `test_pclm_processing()`: 测试PCLm处理功能
- 验证Ghostscript转换的可用性和正确性

### 配置和依赖文件

#### 4. `requirements.txt` (34行)
**依赖管理文件**

**核心依赖：**
- `scapy>=2.4.0`: 网络数据包捕获和分析
- Python标准库模块：asyncio, socket, logging, pathlib, datetime, zlib等

**系统依赖：**
- Windows: Npcap
- Linux: libpcap-dev

#### 5. `install_dependencies.bat` (75行)
**Windows依赖安装脚本**

**功能：**
- 检查Python安装
- 安装scapy库
- 提供Npcap安装指导
- 验证安装结果

#### 6. `start_print_sniffer.bat` (51行)
**Windows启动脚本**

**功能：**
- 检查管理员权限
- 验证Python和必要文件
- 启动打印流量嗅探器

### 文档文件

#### 7. `README.md` (268行)
**主要说明文档**

**包含内容：**
- 项目功能特性介绍
- 安装和使用指南
- 命令行参数说明
- 故障排除指南
- 技术实现架构

#### 8. `README_Linux.md`
**Linux专用使用指南**

**包含内容：**
- Linux环境安装步骤
- 权限和安全配置
- 网络接口选择
- systemd服务配置
- 性能优化建议

#### 9. `项目总结.md` (228行)
**项目总结文档**

**包含内容：**
- 项目概述和实现功能
- 技术架构设计
- 性能特点分析
- 安全考虑
- 扩展可能性

### 其他文件

#### 10. `sasasa.md` (12427行)
**网络数据包分析示例**

包含详细的Wireshark数据包捕获分析，展示了IPv6环境下打印机通信的完整过程，包括TCP连接建立、HTTP/SOAP通信等。

#### 11. `Ghostscript/` 目录
**Ghostscript工具集**

包含：
- `gswin64.exe`: Ghostscript主程序
- `gswin64c.exe`: 控制台版本
- `gsdll64.dll`: 动态链接库
- `gsdll64.lib`: 库文件

## 🔧 核心技术实现

### 数据格式识别
系统支持识别多种打印数据格式：
- **PDF格式**: 通过`%PDF`标识符识别
- **PostScript**: 通过`%!PS`标识符识别
- **PCLm格式**: 通过`PCLm`或`pclm`标识符识别
- **图像格式**: PNG、JPEG、GIF、BMP等
- **压缩数据**: zlib、gzip压缩流
- **Windows格式**: XPS、EMF、WMF等

### TCP流重组技术
- 智能数据包排序和去重
- 序列号管理和间隙检测
- 数据完整性验证
- 超时流清理机制

### PCLm处理增强
- HTTP/SOAP包装数据提取
- Ghostscript集成转换
- PDF黑色横杠问题修复
- 多种回退处理机制

### 网络协议支持
- IPv4/IPv6双栈支持
- 多种打印协议端口监听
- 异步网络处理
- 连接状态管理

## 🎯 主要功能特性

### 智能流量监听
- 实时网络数据包捕获
- 多协议端口同时监听
- 智能打印流量过滤
- TCP流自动重组

### 数据处理能力
- 多格式自动识别
- 压缩数据自动解压
- PCLm到PDF转换
- 文件完整性验证

### 用户友好特性
- 详细中文日志记录
- 实时进度显示
- 自动文件命名
- 错误处理和恢复

### 跨平台支持
- Windows/Linux/macOS兼容
- 平台特定安装指导
- 权限管理优化
- 系统服务集成

## 📊 性能特点

### 并发处理能力
- 支持多客户端同时连接
- 异步数据处理
- 内存使用优化
- CPU占用控制

### 数据处理能力
- 支持GB级别文件
- 实时数据流处理
- 智能缓冲管理
- 高效格式转换

### 稳定性保障
- 完善错误处理
- 自动恢复机制
- 资源清理管理
- 7x24小时运行支持

这个项目展现了网络编程、数据处理、格式转换等多个技术领域的综合应用，是一个功能完整、设计优良的打印流量监听和数据捕获系统。

## 🔍 详细代码分析

### print_sniffer_pcap.py 核心实现

#### EnhancedPrintDataProcessor类详细分析

**格式检测机制 (detect_format方法)**
```python
def detect_format(self, data):
    """智能格式检测，支持多种打印数据格式"""
    # PDF格式检测
    if data.startswith(b'%PDF'):
        return 'pdf'

    # PostScript格式检测
    if data.startswith(b'%!PS'):
        return 'ps'

    # PCLm格式检测（支持多种变体）
    if b'PCLm' in data[:1000] or b'pclm' in data[:1000]:
        return 'pclm'

    # 图像格式检测
    if data.startswith(b'\x89PNG'):
        return 'png'
    if data.startswith(b'\xff\xd8\xff'):
        return 'jpeg'

    # 压缩数据检测
    if data.startswith(b'\x1f\x8b'):
        return 'gzip'
    if data.startswith(b'x\x9c') or data.startswith(b'x\x01'):
        return 'zlib'
```

**PCLm处理增强功能**
- HTTP/SOAP包装检测：识别被HTTP协议包装的PCLm数据
- Ghostscript集成：自动调用Ghostscript进行PCLm到PDF转换
- 错误恢复：转换失败时保留原始PCLm数据
- 格式验证：确保转换后的PDF文件完整性

**文件命名策略**
```python
def generate_filename(self, format_type, client_addr):
    """生成带时间戳和客户端信息的文件名"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
    client_ip = client_addr.replace(':', '_')
    return f"print_data_{timestamp}_{client_ip}.{format_type}"
```

#### PrintSniffer类详细分析

**数据包过滤机制**
```python
def is_print_packet(self, packet):
    """判断是否为打印相关数据包"""
    if not packet.haslayer(TCP):
        return False

    tcp_layer = packet[TCP]
    # 检查目标端口是否为打印端口
    if tcp_layer.dport in self.target_ports:
        return True

    # WSD打印服务检测
    if self.enable_wsd and tcp_layer.dport in [3910, 9220]:
        return True

    return False
```

**TCP流重组算法**
- 序列号排序：确保数据包按正确顺序重组
- 重复包检测：避免重复数据影响文件完整性
- 间隙检测：识别丢失的数据包
- 超时管理：自动清理长时间未完成的流

**数据完整性检测**
```python
def _is_data_complete(self, stream_key):
    """检测TCP流数据是否完整"""
    stream_data = self.tcp_streams[stream_key]
    data = stream_data['data']

    # PDF完整性检测
    if data.startswith(b'%PDF'):
        return b'%%EOF' in data[-100:]

    # HTTP Content-Length检测
    if b'Content-Length:' in data[:1000]:
        # 解析Content-Length并验证数据长度
        return self._verify_http_content_length(data)

    # 其他格式的完整性检测
    return self._check_format_completeness(data)
```

### 测试模块分析

#### test_data_completeness.py
**测试覆盖范围：**
- PDF文件完整性检测准确性
- HTTP Content-Length解析正确性
- SOAP消息完整性验证
- 各种不完整数据的识别能力

#### test_ghostscript_conversion.py
**测试功能：**
- Ghostscript安装检测
- PCLm到PDF转换功能
- 转换质量验证
- 错误处理机制测试

### 配置和部署

#### Windows环境配置
**install_dependencies.bat脚本功能：**
1. Python环境检测和验证
2. pip包管理器更新
3. scapy库安装和配置
4. Npcap驱动安装指导
5. 权限检查和提升

**start_print_sniffer.bat启动脚本：**
1. 管理员权限验证
2. 必要文件存在性检查
3. Python环境可用性验证
4. 嗅探器启动和监控

#### Linux环境支持
**系统依赖安装：**
```bash
# Ubuntu/Debian
sudo apt-get install python3-dev libpcap-dev

# CentOS/RHEL
sudo yum install python3-devel libpcap-devel

# 权限配置
sudo setcap cap_net_raw,cap_net_admin=eip /usr/bin/python3
```

## 🛠️ 技术架构深度分析

### 网络层实现
**Scapy集成：**
- 原始套接字访问
- 数据包解析和重组
- 协议栈处理
- 网络接口管理

**异步处理架构：**
- 非阻塞数据包捕获
- 并发流处理
- 内存管理优化
- 资源清理机制

### 数据处理流水线
1. **数据包捕获** → Scapy网络嗅探
2. **协议解析** → TCP/IP栈处理
3. **流重组** → 序列号排序和去重
4. **格式识别** → 多格式智能检测
5. **数据转换** → PCLm/压缩数据处理
6. **文件保存** → 带时间戳的文件输出

### 错误处理和恢复
**多层错误处理：**
- 网络层错误：接口断开、权限不足
- 协议层错误：数据包损坏、序列错误
- 应用层错误：格式不支持、转换失败
- 系统层错误：磁盘空间、内存不足

**自动恢复机制：**
- 网络接口重连
- 流状态重置
- 资源清理和重分配
- 日志记录和报告

## 📈 性能优化策略

### 内存管理
- 流式数据处理：避免大文件全部加载到内存
- 智能缓冲：根据数据类型调整缓冲区大小
- 及时清理：完成处理后立即释放资源
- 内存监控：防止内存泄漏和溢出

### CPU优化
- 异步处理：避免阻塞主线程
- 批量操作：减少系统调用开销
- 算法优化：高效的数据结构和算法
- 多核利用：并行处理多个数据流

### 网络性能
- 包过滤优化：减少不必要的数据包处理
- 缓冲区调优：平衡内存使用和处理延迟
- 连接池管理：复用网络连接
- 流量控制：防止网络拥塞

## 🔒 安全考虑

### 权限管理
- 最小权限原则：仅请求必要的系统权限
- 用户权限检查：验证运行用户权限
- 文件权限控制：安全的文件创建和访问
- 网络权限限制：仅监听指定端口和接口

### 数据安全
- 敏感数据处理：避免在日志中记录敏感信息
- 临时文件清理：及时删除临时和缓存文件
- 数据完整性：验证捕获数据的完整性
- 访问控制：限制输出文件的访问权限

### 网络安全
- 流量过滤：仅处理打印相关流量
- 协议验证：验证网络协议的合法性
- 攻击防护：防止网络攻击和恶意数据
- 监控日志：记录所有网络活动

这个打印流量嗅探器项目体现了现代网络应用开发的最佳实践，在功能完整性、性能优化、安全性和可维护性方面都达到了较高的水准。

## 📚 使用指南和最佳实践

### 命令行参数详解

#### 基本使用
```bash
# 基本启动（监听所有打印端口）
python print_sniffer_pcap.py

# 指定输出目录
python print_sniffer_pcap.py --output-dir /path/to/output

# 指定网络接口
python print_sniffer_pcap.py --interface eth0

# 启用详细日志
python print_sniffer_pcap.py --verbose
```

#### 高级配置
```bash
# 自定义端口监听
python print_sniffer_pcap.py --ports 9100,515,631

# 启用WSD打印服务监听
python print_sniffer_pcap.py --enable-wsd

# 设置最大流大小（防止内存溢出）
python print_sniffer_pcap.py --max-stream-size 100MB

# 组合使用
python print_sniffer_pcap.py --output-dir ./captures --interface Wi-Fi --enable-wsd --verbose
```

### 配置文件说明

#### 环境变量配置
```bash
# Windows
set PRINT_SNIFFER_OUTPUT_DIR=C:\PrintCaptures
set PRINT_SNIFFER_INTERFACE=Wi-Fi
set PRINT_SNIFFER_VERBOSE=1

# Linux/macOS
export PRINT_SNIFFER_OUTPUT_DIR=/var/log/print-captures
export PRINT_SNIFFER_INTERFACE=eth0
export PRINT_SNIFFER_VERBOSE=1
```

#### 日志配置
```python
# 日志级别设置
logging.basicConfig(
    level=logging.INFO,  # DEBUG, INFO, WARNING, ERROR
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('print_sniffer.log'),
        logging.StreamHandler()
    ]
)
```

### 故障排除指南

#### 常见问题及解决方案

**1. 权限不足错误**
```
错误：Permission denied (需要管理员权限)
解决：
- Windows: 以管理员身份运行命令提示符
- Linux: 使用sudo或配置cap_net_raw权限
- macOS: 使用sudo或配置网络权限
```

**2. 网络接口问题**
```
错误：No suitable interface found
解决：
- 检查网络接口名称：ip addr show (Linux) 或 ipconfig (Windows)
- 确认接口状态：确保网络接口处于活动状态
- 使用正确的接口名称：--interface 参数指定正确接口
```

**3. Scapy安装问题**
```
错误：ModuleNotFoundError: No module named 'scapy'
解决：
- 安装scapy：pip install scapy
- Windows额外要求：安装Npcap驱动
- Linux额外要求：安装libpcap-dev
```

**4. Ghostscript转换问题**
```
错误：Ghostscript not found or conversion failed
解决：
- 安装Ghostscript：从官网下载并安装
- 配置PATH：确保gswin64.exe在系统PATH中
- 检查权限：确保有执行Ghostscript的权限
```

#### 性能调优建议

**内存优化：**
```python
# 调整最大流大小
MAX_STREAM_SIZE = 50 * 1024 * 1024  # 50MB

# 调整清理间隔
CLEANUP_INTERVAL = 300  # 5分钟

# 调整超时时间
STREAM_TIMEOUT = 600  # 10分钟
```

**网络优化：**
```python
# 调整数据包缓冲区
PACKET_BUFFER_SIZE = 65536

# 调整处理批次大小
BATCH_SIZE = 100

# 启用数据包过滤
BPF_FILTER = "tcp and (port 9100 or port 515 or port 631)"
```

### 监控和维护

#### 日志监控
```bash
# 实时查看日志
tail -f print_sniffer.log

# 搜索错误日志
grep "ERROR" print_sniffer.log

# 统计捕获文件数量
ls -la captures/ | wc -l
```

#### 系统监控
```bash
# 监控内存使用
ps aux | grep print_sniffer

# 监控网络连接
netstat -an | grep :9100

# 监控磁盘使用
df -h captures/
```

#### 定期维护
```bash
# 清理旧的捕获文件（保留30天）
find captures/ -name "*.pdf" -mtime +30 -delete

# 压缩日志文件
gzip print_sniffer.log.old

# 检查系统资源
free -h && df -h
```

## 🔧 扩展开发指南

### 添加新的文件格式支持

#### 1. 扩展格式检测
```python
def detect_format(self, data):
    # 现有格式检测...

    # 添加新格式检测
    if data.startswith(b'NEW_FORMAT_SIGNATURE'):
        return 'new_format'

    # XPS格式检测
    if b'application/vnd.ms-xpsdocument' in data[:1000]:
        return 'xps'
```

#### 2. 添加格式处理器
```python
def process_new_format_data(self, data):
    """处理新格式数据"""
    try:
        # 格式特定的处理逻辑
        processed_data = self._convert_new_format(data)
        return processed_data
    except Exception as e:
        self.logger.error(f"新格式处理失败: {e}")
        return data  # 返回原始数据
```

#### 3. 更新保存逻辑
```python
def save_data(self, data, client_id):
    format_type = self.detect_format(data)

    if format_type == 'new_format':
        data = self.process_new_format_data(data)
        format_type = 'converted_format'

    # 继续现有保存逻辑...
```

### 添加新的网络协议支持

#### 1. 扩展端口监听
```python
# 在PrintSniffer类中添加新端口
DEFAULT_PRINT_PORTS = [
    9100,  # AppSocket/JetDirect
    515,   # LPD
    631,   # IPP
    445,   # SMB
    139,   # NetBIOS
    # 添加新协议端口
    8080,  # 自定义HTTP打印
    9999,  # 厂商特定端口
]
```

#### 2. 协议特定处理
```python
def process_custom_protocol(self, packet):
    """处理自定义协议数据包"""
    if packet[TCP].dport == 8080:
        # HTTP打印协议处理
        return self._process_http_print(packet)
    elif packet[TCP].dport == 9999:
        # 厂商特定协议处理
        return self._process_vendor_protocol(packet)
```

### 性能监控集成

#### 1. 添加性能指标
```python
class PerformanceMonitor:
    def __init__(self):
        self.packet_count = 0
        self.bytes_processed = 0
        self.files_saved = 0
        self.start_time = time.time()

    def update_stats(self, packet_size, file_saved=False):
        self.packet_count += 1
        self.bytes_processed += packet_size
        if file_saved:
            self.files_saved += 1

    def get_stats(self):
        runtime = time.time() - self.start_time
        return {
            'packets_per_second': self.packet_count / runtime,
            'bytes_per_second': self.bytes_processed / runtime,
            'files_per_hour': self.files_saved / (runtime / 3600)
        }
```

#### 2. 集成监控API
```python
def start_monitoring_server(self, port=8888):
    """启动HTTP监控服务器"""
    from http.server import HTTPServer, BaseHTTPRequestHandler
    import json

    class MonitorHandler(BaseHTTPRequestHandler):
        def do_GET(self):
            if self.path == '/stats':
                stats = self.server.sniffer.performance_monitor.get_stats()
                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps(stats).encode())

    server = HTTPServer(('localhost', port), MonitorHandler)
    server.sniffer = self
    server.serve_forever()
```

## 🎯 项目总结

### 技术亮点
1. **多协议支持**：覆盖主流打印协议和端口
2. **智能格式识别**：支持多种文件格式的自动检测
3. **高效流重组**：可靠的TCP流重组和数据完整性保证
4. **跨平台兼容**：Windows、Linux、macOS全平台支持
5. **扩展性设计**：模块化架构便于功能扩展

### 应用场景
- **企业网络监控**：监控公司内部打印活动
- **安全审计**：记录和分析打印数据流
- **故障诊断**：分析打印问题和网络故障
- **研究开发**：打印协议研究和新功能开发
- **合规检查**：满足数据保护和审计要求

### 未来发展方向
1. **云端集成**：支持云打印服务监控
2. **AI分析**：智能内容分析和分类
3. **实时告警**：异常打印行为检测
4. **Web界面**：图形化管理和监控界面
5. **集群部署**：支持大规模分布式部署

这个项目不仅是一个功能完整的打印流量监控工具，更是网络编程、数据处理、系统集成等多个技术领域的优秀实践案例。

## 📁 补充文件分析

### 缺失的核心Python文件

根据项目文档和`__pycache__`目录中的编译文件，项目还包含以下重要的Python模块：

#### 1. `print_capture_server.py` (推测约300行)
**功能：TCP服务器模式的打印数据捕获**

**推测的核心功能：**
- **PrintCaptureServer类**：主服务器类，实现异步TCP服务器
- **异步连接处理**：使用asyncio处理多客户端并发连接
- **端口监听**：同时监听9100和3910端口
- **数据接收**：完整接收打印数据流
- **连接管理**：处理连接超时和断开
- **优雅关闭**：支持Ctrl+C信号处理

**预期的类结构：**
```python
class PrintCaptureServer:
    def __init__(self, ports, output_dir):
        """初始化服务器"""

    async def handle_client(self, reader, writer):
        """处理客户端连接"""

    async def start_server(self):
        """启动服务器"""

    def stop_server(self):
        """停止服务器"""
```

#### 2. `config.py` (推测约100行)
**功能：配置管理模块**

**推测的配置内容：**
```python
SERVER_CONFIG = {
    'ports': [9100, 3910],
    'output_directory': './captured_prints',
    'connection_timeout': 30,
    'buffer_size': 8192,
    'max_connections': 100,
    'log_level': 'INFO',
    'log_file': 'print_capture.log'
}

PRINT_FORMATS = {
    'pdf': {'signature': b'%PDF', 'extension': '.pdf'},
    'ps': {'signature': b'%!PS', 'extension': '.ps'},
    'pclm': {'signature': b'PCLm', 'extension': '.pdf'},
    'png': {'signature': b'\x89PNG', 'extension': '.png'},
    'jpeg': {'signature': b'\xff\xd8\xff', 'extension': '.jpg'}
}
```

#### 3. `start_server.py` (推测约200行)
**功能：服务器启动脚本**

**推测的功能：**
- 命令行参数解析
- 配置文件加载
- 端口可用性检查
- 服务器启动和监控
- 信号处理和优雅关闭

**预期的命令行接口：**
```python
def main():
    parser = argparse.ArgumentParser(description='打印捕获服务器')
    parser.add_argument('-p', '--ports', nargs='+', type=int,
                       default=[9100, 3910], help='监听端口')
    parser.add_argument('-o', '--output', default='./captured_prints',
                       help='输出目录')
    parser.add_argument('-l', '--log-level', default='INFO',
                       help='日志级别')
    # ... 更多参数
```

#### 4. `test_client.py` (推测约200行)
**功能：测试客户端**

**推测的测试功能：**
- PDF数据发送测试
- PostScript数据发送测试
- PCLm数据发送测试
- 多连接并发测试
- 压力测试和性能测试

**预期的测试方法：**
```python
class PrintTestClient:
    def send_pdf_test(self):
        """发送PDF测试数据"""

    def send_ps_test(self):
        """发送PostScript测试数据"""

    def send_pclm_test(self):
        """发送PCLm测试数据"""

    def multi_connection_test(self):
        """多连接并发测试"""
```

#### 5. `print_traffic_interceptor.py` (推测约300行)
**功能：原始套接字流量拦截器**

**推测的功能：**
- 原始套接字创建和管理
- 网络流量拦截
- 数据包过滤和解析
- 与print_sniffer_pcap.py的替代实现

#### 6. PCLm处理模块

**`mupdf_pclm_converter.py`**
- 使用MuPDF库进行PCLm转换
- 提供Ghostscript的替代方案

**`pclm_processor.py`**
- PCLm格式的专用处理器
- 格式解析和数据提取

**`pymupdf_pclm_converter.py`**
- 基于PyMuPDF的PCLm转换器
- Python原生的PDF处理方案

### 项目架构总览

```
打印流量监控系统
├── 网络流量嗅探模式 (print_sniffer_pcap.py)
│   ├── Scapy数据包捕获
│   ├── TCP流重组
│   ├── 格式识别和转换
│   └── 文件保存
│
├── TCP服务器模式 (print_capture_server.py)
│   ├── 异步TCP服务器
│   ├── 多客户端处理
│   ├── 数据接收和处理
│   └── 文件保存
│
├── 配置管理 (config.py)
│   ├── 服务器配置
│   ├── 格式定义
│   └── 系统参数
│
├── 启动脚本 (start_server.py)
│   ├── 参数解析
│   ├── 环境检查
│   └── 服务器启动
│
├── 测试工具 (test_client.py)
│   ├── 功能测试
│   ├── 性能测试
│   └── 压力测试
│
├── PCLm处理模块
│   ├── Ghostscript转换器
│   ├── MuPDF转换器
│   └── PyMuPDF转换器
│
└── 辅助工具
    ├── 依赖安装脚本
    ├── 启动脚本
    └── 测试脚本
```

### 双模式架构设计

#### 模式1：网络流量嗅探模式
**特点：**
- 被动监听网络流量
- 无需修改打印机配置
- 支持多种网络协议
- 实时流量分析

**适用场景：**
- 现有网络环境监控
- 不能修改打印机设置
- 需要监控所有打印流量
- 网络安全审计

#### 模式2：TCP服务器模式
**特点：**
- 主动接收打印数据
- 需要配置打印机指向服务器
- 高效的数据处理
- 稳定的连接管理

**适用场景：**
- 可控的打印环境
- 高性能数据处理需求
- 稳定的长期运行
- 企业级部署

### 技术选型对比

| 技术组件 | 流量嗅探模式 | TCP服务器模式 |
|---------|-------------|--------------|
| 网络库 | Scapy | asyncio + socket |
| 数据获取 | 被动嗅探 | 主动接收 |
| 权限要求 | 管理员权限 | 普通权限 |
| 配置复杂度 | 低 | 中等 |
| 性能开销 | 中等 | 低 |
| 稳定性 | 中等 | 高 |
| 扩展性 | 高 | 高 |

### 部署建议

#### 开发环境
- 使用TCP服务器模式进行功能开发
- 使用test_client.py进行功能测试
- 启用DEBUG日志级别

#### 测试环境
- 同时部署两种模式进行对比测试
- 使用真实打印机进行集成测试
- 进行性能和稳定性测试

#### 生产环境
- 根据实际需求选择合适的模式
- 配置适当的日志级别和监控
- 设置自动启动和故障恢复

这个双模式架构设计为不同的使用场景提供了灵活的解决方案，既满足了被动监控的需求，也提供了高性能的主动数据处理能力。
