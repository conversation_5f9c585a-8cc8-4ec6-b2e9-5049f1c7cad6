@echo off
chcp 65001 >nul
title Install Print Sniffer Dependencies

echo ========================================
echo     Print Sniffer Dependencies Setup
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.7+ and try again
    echo Download from: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Python version:
python --version
echo.

echo Installing Python dependencies...
echo.

REM Install scapy
echo Installing scapy...
pip install scapy
if errorlevel 1 (
    echo Failed to install scapy
    echo Trying with alternative source...
    pip install -i https://pypi.tuna.tsinghua.edu.cn/simple scapy
)

echo.
echo Checking installation...
python -c "import scapy; print('Scapy installed successfully')" 2>nul
if errorlevel 1 (
    echo Warning: Scapy installation may have issues
) else (
    echo Scapy installation verified
)

echo.
echo ========================================
echo     Windows Npcap Installation
echo ========================================
echo.
echo For Windows users, you also need to install Npcap:
echo.
echo 1. Visit: https://nmap.org/npcap/
echo 2. Download the latest Npcap installer
echo 3. Run the installer as Administrator
echo 4. Make sure to check "Install Npcap in WinPcap API-compatible Mode"
echo.
echo After installing Npcap, you can run:
echo   python print_sniffer_pcap.py
echo.

set /p install_npcap="Do you want to open the Npcap download page? (y/n): "
if /i "%install_npcap%"=="y" (
    start https://nmap.org/npcap/
)

echo.
echo Installation completed!
echo.
echo Next steps:
echo 1. Install Npcap if you haven't already
echo 2. Run as Administrator: python print_sniffer_pcap.py
echo 3. Or use the raw socket version: python print_traffic_interceptor.py
echo.
pause
