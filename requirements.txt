# 打印数据捕获和流量监听器依赖
# Print Data Capture and Traffic Sniffer Dependencies

# Python版本要求 / Python version requirement: >= 3.7

# 核心依赖 / Core dependencies:
# 基础TCP服务器模式 (print_capture_server.py) 仅使用标准库
# Basic TCP server mode uses only standard library

# 流量监听模式依赖 / Traffic sniffing mode dependencies:
scapy>=2.4.0  # 网络数据包捕获和分析 (for packet capture and analysis)

# 标准库模块 / Standard library modules used:
# - asyncio (异步编程)
# - socket (网络编程)
# - logging (日志记录)
# - pathlib (路径处理)
# - datetime (时间处理)
# - zlib (压缩处理)
# - struct (二进制数据处理)
# - io (输入输出)
# - signal (信号处理)
# - threading (多线程)
# - argparse (命令行参数解析)
# - collections (数据结构)

# 系统依赖 / System dependencies:
# Windows: Npcap (https://nmap.org/npcap/)
# Linux: libpcap-dev (sudo apt-get install libpcap-dev)

# 可选依赖 / Optional dependencies (for enhanced functionality):
# Pillow>=8.0.0  # 用于高级图像处理 (for advanced image processing)
# PyPDF2>=2.0.0  # 用于PDF文件详细分析 (for detailed PDF analysis)
