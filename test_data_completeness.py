#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据完整性检测功能
"""

import sys
import logging
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from print_sniffer_pcap import PrintSniffer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_data_completeness():
    """测试数据完整性检测"""
    print("=== 测试数据完整性检测功能 ===")
    
    sniffer = PrintSniffer()
    
    # 测试1: 完整的PDF数据
    print("\n1. 测试完整的PDF数据")
    test_stream_key = "test_stream_1"
    sniffer.tcp_streams[test_stream_key] = {
        'packets': [{
            'seq': 1000,
            'payload': b'%PDF-1.7\n1 0 obj\n<<\n/Type /Catalog\n>>\nendobj\nxref\n0 1\ntrailer\n<<\n/Size 1\n>>\nstartxref\n%%EOF',
            'data_len': 50
        }],
        'start_time': 0,
        'last_activity': 0,
        'base_seq': 1000,
        'expected_seq': 1000
    }
    
    result = sniffer._is_data_complete(test_stream_key)
    print(f"   完整PDF检测结果: {'✅ 通过' if result else '❌ 失败'}")
    
    # 测试2: 不完整的PDF数据
    print("\n2. 测试不完整的PDF数据")
    test_stream_key2 = "test_stream_2"
    sniffer.tcp_streams[test_stream_key2] = {
        'packets': [{
            'seq': 1000,
            'payload': b'%PDF-1.7\n1 0 obj\n<<\n/Type /Catalog\n>>\nendobj',
            'data_len': 30
        }],
        'start_time': 0,
        'last_activity': 0,
        'base_seq': 1000,
        'expected_seq': 1000
    }
    
    result = sniffer._is_data_complete(test_stream_key2)
    print(f"   不完整PDF检测结果: {'✅ 通过' if not result else '❌ 失败'}")
    
    # 测试3: 完整的SOAP数据
    print("\n3. 测试完整的SOAP数据")
    test_stream_key3 = "test_stream_3"
    soap_data = b'''<?xml version="1.0"?>
<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
<soap:Body>
<test>data</test>
</soap:Body>
</soap:Envelope>'''
    
    sniffer.tcp_streams[test_stream_key3] = {
        'packets': [{
            'seq': 1000,
            'payload': soap_data,
            'data_len': len(soap_data)
        }],
        'start_time': 0,
        'last_activity': 0,
        'base_seq': 1000,
        'expected_seq': 1000
    }
    
    result = sniffer._is_data_complete(test_stream_key3)
    print(f"   完整SOAP检测结果: {'✅ 通过' if result else '❌ 失败'}")
    
    # 测试4: HTTP Content-Length
    print("\n4. 测试HTTP Content-Length")
    test_stream_key4 = "test_stream_4"
    http_data = b'''HTTP/1.1 200 OK
Content-Type: application/pdf
Content-Length: 10

1234567890'''
    
    sniffer.tcp_streams[test_stream_key4] = {
        'packets': [{
            'seq': 1000,
            'payload': http_data,
            'data_len': len(http_data)
        }],
        'start_time': 0,
        'last_activity': 0,
        'base_seq': 1000,
        'expected_seq': 1000
    }
    
    result = sniffer._is_data_complete(test_stream_key4)
    print(f"   HTTP Content-Length检测结果: {'✅ 通过' if result else '❌ 失败'}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_data_completeness()
