# 增强打印流量嗅探器

一个功能完整的Python程序，用于监听本机发送到打印机的数据流量并将其还原为相应格式的文件。

## ✨ 核心特性

- 🕵️ **智能流量监听**：监听本机发送到打印机的网络流量
- 🔧 **增强PCLm处理**：自动解决PDF文件中的黑色横杠问题
- 📊 **多格式支持**：支持PDF、PostScript、PCLm、PNG、JPEG等格式
- 🌐 **IPv4/IPv6兼容**：同时支持IPv4和IPv6网络协议
- ⚡ **实时处理**：实时捕获、处理并保存打印数据

## 功能特性

### 🌐 网络监听
- 同时监听9100端口（标准打印机端口）和3910端口
- 使用asyncio实现高性能异步TCP服务器
- 支持多客户端并发连接
- 自动处理连接超时和异常断开

### 📊 数据捕获
- 完整接收所有打印数据流
- 自动检测数据传输结束
- 智能缓冲区管理
- 实时显示接收进度

### 🔍 格式识别
- **PDF格式**: 自动识别PDF文档
- **PostScript**: 支持PS/EPS文件
- **PCLm格式**: HP打印机常用格式
- **图像格式**: PNG、JPEG等
- **压缩数据**: zlib压缩流处理
- **原始数据**: 未知格式保存为.prn文件

### 🔄 文件还原
- **PCLm → PDF**: 从PCLm数据中提取PDF内容
- **PostScript → PS**: 保存为标准PS文件  
- **PDF → PDF**: 直接保存PDF文档
- **压缩数据解压**: 自动解压zlib压缩流
- **智能文件命名**: 时间戳+客户端IP格式

### 📝 日志记录
- 详细的中文日志记录
- 连接状态实时监控
- 数据接收进度显示
- 文件保存信息记录
- 错误异常完整追踪

## 文件结构

```
printer/
├── print_capture_server.py    # 主服务器程序
├── start_server.py           # 启动脚本
├── config.py                 # 配置文件
├── test_client.py           # 测试客户端
├── README.md                # 说明文档
└── captured_prints/         # 输出目录（自动创建）
```

## 安装要求

- Python 3.7+
- 标准库（无需额外依赖）

## 快速开始

### 1. 启动服务器

```bash
# 使用默认配置启动
python start_server.py

# 指定端口启动
python start_server.py -p 9100 3910

# 指定输出目录
python start_server.py -o ./my_prints

# 设置日志级别
python start_server.py -l DEBUG
```

### 2. 检查端口可用性

```bash
python start_server.py --check-ports
```

### 3. 测试服务器功能

```bash
# 发送PDF测试数据
python test_client.py pdf

# 发送PostScript测试数据  
python test_client.py ps

# 发送PCLm测试数据
python test_client.py pclm

# 多连接并发测试
python test_client.py multi

# 运行所有测试
python test_client.py all
```

## 使用方法

### 基本使用

1. **启动服务器**
   ```bash
   python start_server.py
   ```

2. **配置打印机**
   - 将打印机IP设置为运行服务器的计算机IP
   - 端口设置为9100或3910

3. **开始打印**
   - 从任何应用程序发送打印任务
   - 服务器将自动捕获并保存文件

### 高级配置

#### 命令行参数

```bash
python start_server.py [选项]

选项:
  -p, --ports PORT [PORT ...]   监听端口列表 (默认: [9100, 3910])
  -o, --output DIR              输出目录 (默认: ./captured_prints)
  -l, --log-level LEVEL         日志级别 (DEBUG/INFO/WARNING/ERROR)
  --log-file FILE               日志文件路径
  --timeout SECONDS             连接超时时间
  --check-ports                 检查端口可用性
  --test-mode                   测试模式
```

#### 配置文件修改

编辑 `config.py` 文件可以修改更多高级设置：

```python
SERVER_CONFIG = {
    'ports': [9100, 3910],
    'output_directory': './captured_prints',
    'connection_timeout': 30,
    'buffer_size': 8192,
    # ... 更多配置
}
```

## 输出文件格式

### 文件命名规则

```
print_YYYYMMDD_HHMMSS_客户端IP.扩展名
```

示例：
- `print_20241225_143022_192_168_1_100.pdf`
- `print_20241225_143025_10_0_0_5.ps`

### 支持的输出格式

| 输入格式 | 输出扩展名 | 说明 |
|---------|-----------|------|
| PDF | .pdf | 直接保存PDF文档 |
| PostScript | .ps | 保存为PS文件 |
| PCLm | .pdf | 提取PDF内容或保存原始数据 |
| PNG | .png | 保存PNG图像 |
| JPEG | .jpg | 保存JPEG图像 |
| 压缩数据 | .bin | 解压后的二进制数据 |
| 原始数据 | .prn | 未识别格式的原始数据 |
| 未知格式 | .dat | 无法识别的数据 |

## 日志信息

### 控制台输出示例

```
2024-12-25 14:30:22,123 - INFO - 打印捕获服务器已启动
2024-12-25 14:30:22,124 - INFO - 监听端口: 9100, 3910
2024-12-25 14:30:22,125 - INFO - 输出目录: C:\Users\<USER>\Desktop\printer\captured_prints
2024-12-25 14:30:45,200 - INFO - 新连接建立 - 端口 9100, 客户端: *************:52341
2024-12-25 14:30:45,250 - INFO - 检测到数据格式: pdf
2024-12-25 14:30:46,100 - INFO - 文件保存成功: captured_prints\print_20241225_143045_192_168_1_100.pdf (大小: 2048 字节)
```

### 打印任务详细信息

```
打印任务完成:
- 客户端: *************:52341
- 端口: 9100
- 接收数据: 2048 字节
- 保存文件: captured_prints\print_20241225_143045_192_168_1_100.pdf
- 文件大小: 2048 字节
- 持续时间: 1.25 秒
```

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口使用情况
   netstat -an | findstr :9100
   
   # 使用其他端口
   python start_server.py -p 9101 3911
   ```

2. **权限不足**
   - Windows: 以管理员身份运行
   - Linux: 使用sudo或修改端口到1024以上

3. **防火墙阻止**
   - 添加Python程序到防火墙例外
   - 开放相应端口的入站规则

4. **文件保存失败**
   - 检查输出目录权限
   - 确保磁盘空间充足

### 调试模式

```bash
# 启用详细日志
python start_server.py -l DEBUG

# 查看日志文件
type print_capture.log
```

## 技术实现

### 核心技术栈
- **异步编程**: asyncio实现高并发
- **网络编程**: socket TCP服务器
- **数据处理**: 二进制流解析
- **文件格式**: PDF/PS/PCLm格式识别
- **压缩处理**: zlib解压缩

### 架构设计
- **PrintCaptureServer**: 主服务器类，处理网络连接
- **PrintDataProcessor**: 数据处理器，负责格式识别和文件保存
- **异步连接处理**: 每个客户端连接独立处理
- **优雅关闭**: 支持Ctrl+C信号处理

## 许可证

本项目为开源软件，仅供学习和研究使用。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

---

**注意**: 请确保在合法和授权的环境中使用此工具，遵守相关法律法规和隐私政策。
