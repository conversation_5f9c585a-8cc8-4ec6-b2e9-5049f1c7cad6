#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于Scapy的打印流量嗅探器
功能：监听本机发送到打印机的数据流量并还原为文件
依赖：pip install scapy
作者：AI助手
"""

import logging
import sys
import time
import threading
from datetime import datetime
from pathlib import Path
from collections import defaultdict
import io
import re
import zlib

import platform
import os
import subprocess
import tempfile

try:
    from scapy.all import sniff, TCP, IP, IPv6, Raw
    SCAPY_AVAILABLE = True
except ImportError:
    SCAPY_AVAILABLE = False
    print("❌ 未安装scapy库")
    print("请运行: pip install scapy")

    # 平台特定的安装提示
    current_platform = platform.system().lower()
    if current_platform == "windows":
        print("Windows用户还需要安装Npcap: https://nmap.org/npcap/")
    elif current_platform == "linux":
        print("Linux用户可能需要以root权限运行或安装libpcap-dev:")
        print("  Ubuntu/Debian: sudo apt-get install libpcap-dev")
        print("  CentOS/RHEL: sudo yum install libpcap-devel")
        print("  运行时: sudo python3 print_sniffer_pcap.py")
    elif current_platform == "darwin":
        print("macOS用户可能需要安装libpcap:")
        print("  brew install libpcap")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('print_sniffer.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class EnhancedPrintDataProcessor:
    """增强的打印数据处理器 - 集成版本"""

    def __init__(self, output_dir: str = "./captured_prints"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)

        # 扩展的文件格式标识
        self.format_signatures = {
            # PDF相关
            b'%PDF': 'pdf',
            b'PCLm': 'pclm',

            # PostScript相关
            b'%!PS': 'postscript',
            b'%!PS-Adobe': 'postscript',

            # PCL相关
            b'\x1b%-12345X': 'pjl',  # PJL (Printer Job Language)
            b'@PJL': 'pjl',
            b'\x1bE': 'pcl',         # PCL Reset
            b'\x1b&': 'pcl',         # PCL命令
            b'\x1b*': 'pcl',         # PCL图形命令
            b'\x1b(': 'pcl',         # PCL字体命令

            # ESC/P (Epson)
            b'\x1b@': 'escp',        # ESC/P Reset
            b'\x1b(': 'escp',        # ESC/P命令

            # 图像格式
            b'\x89PNG': 'png',
            b'\xff\xd8\xff': 'jpeg',
            b'GIF87a': 'gif',
            b'GIF89a': 'gif',
            b'BM': 'bmp',            # Windows Bitmap

            # Windows特定格式
            b'<?xml': 'xps',         # XPS (XML Paper Specification)
            b'\x01\x00\x00\x00': 'emf',  # Enhanced Metafile
            b'\xd7\xcd\xc6\x9a': 'wmf',  # Windows Metafile

            # 压缩格式
            b'PK\x03\x04': 'zip',    # ZIP (XPS内部使用)
            b'\x1f\x8b': 'gzip',     # GZIP

            # 其他
            b'RIFF': 'riff',         # 某些打印机使用的格式
        }

    def detect_format(self, data: bytes) -> str:
        """检测数据格式"""
        if not data:
            return 'unknown'

        header = data[:1024]

        # 首先检测HTTP/SOAP包装的PCLM数据
        if self._is_http_wrapped_pclm(data):
            logger.info("检测到HTTP/SOAP包装的PCLm格式")
            return 'pclm'

        for signature, format_type in self.format_signatures.items():
            if signature in header:
                logger.info(f"检测到数据格式: {format_type}")
                return format_type

        # 更精确的PCLm格式检测
        if b'PCLm' in data or b'pclm' in data.lower() or b'strip-height' in data:
            logger.info("检测到PCLm格式")
            return 'pclm'

        # 2. 深度内容分析
        try:
            # PCL格式深度检测
            if self._is_pcl_format(data):
                logger.info("检测到PCL格式 (深度分析)")
                return 'pcl'

            # XPS格式检测
            if self._is_xps_format(data):
                logger.info("检测到XPS格式 (深度分析)")
                return 'xps'

        except Exception as e:
            logger.debug(f"深度格式检测时出错: {e}")

        # 3. 检查压缩数据
        try:
            if data.startswith(b'\x78\x9c') or data.startswith(b'\x78\x01') or data.startswith(b'\x78\xda'):
                zlib.decompress(data[:100])
                logger.info("检测到zlib压缩数据")
                return 'compressed'
        except:
            pass

        # 4. 文本格式检测
        try:
            decoded = data[:1024].decode('utf-8', errors='ignore')
            if any(keyword in decoded.lower() for keyword in ['postscript', 'pdf', 'xml', 'html']):
                logger.info("检测到文本格式数据")
                return 'text'
        except:
            pass

        logger.warning("未能识别数据格式，将保存为原始数据")
        return 'raw'

    def _is_pcl_format(self, data: bytes) -> bool:
        """深度检测PCL格式"""
        try:
            # PCL命令特征
            pcl_patterns = [
                b'\x1bE',           # PCL Reset
                b'\x1b&l',          # 页面设置
                b'\x1b*r',          # 光栅图形
                b'\x1b*t',          # 图形分辨率
                b'\x1b*p',          # 光标定位
                b'\x1b(s',          # 字体选择
                b'\x1b&a',          # 光标定位
            ]

            sample = data[:4096]
            pcl_count = sum(1 for pattern in pcl_patterns if pattern in sample)

            # 如果找到多个PCL命令，认为是PCL格式
            return pcl_count >= 2

        except Exception:
            return False

    def _get_ghostscript_path(self) -> str:
        """获取Ghostscript可执行文件路径"""
        try:
            # 首先尝试项目目录中的Ghostscript
            project_gs_path = Path(__file__).parent / "Ghostscript" / "gswin64.exe"
            if project_gs_path.exists():
                logger.info(f"找到项目Ghostscript: {project_gs_path}")
                return str(project_gs_path)

            # 尝试系统PATH中的Ghostscript
            try:
                result = subprocess.run(['gswin64', '--version'],
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    logger.info("找到系统Ghostscript: gswin64")
                    return 'gswin64'
            except (subprocess.TimeoutExpired, FileNotFoundError):
                pass

            # 尝试gswin64c
            try:
                result = subprocess.run(['gswin64c', '--version'],
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    logger.info("找到系统Ghostscript: gswin64c")
                    return 'gswin64c'
            except (subprocess.TimeoutExpired, FileNotFoundError):
                pass

            logger.warning("未找到可用的Ghostscript")
            return None

        except Exception as e:
            logger.error(f"检查Ghostscript路径时出错: {e}")
            return None

    def _convert_pclm_with_ghostscript(self, pclm_data: bytes) -> bytes:
        """使用Ghostscript转换PCLM到PDF"""
        gs_path = self._get_ghostscript_path()
        if not gs_path:
            logger.error("Ghostscript不可用，无法进行PCLM转换")
            return None

        temp_input = None
        temp_output = None

        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix='.pclm', delete=False) as temp_in:
                temp_input = temp_in.name
                temp_in.write(pclm_data)

            with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_out:
                temp_output = temp_out.name

            # 构建Ghostscript命令
            gs_cmd = [
                gs_path,
                '-dNOPAUSE',
                '-dBATCH',
                '-dSAFER',
                '-sDEVICE=pdfwrite',
                '-dPDFSETTINGS=/printer',
                '-dCompatibilityLevel=1.4',
                '-dAutoRotatePages=/None',
                '-dColorImageResolution=300',
                '-dGrayImageResolution=300',
                '-dMonoImageResolution=1200',
                f'-sOutputFile={temp_output}',
                temp_input
            ]

            logger.info("开始使用Ghostscript转换PCLM...")
            logger.debug(f"Ghostscript命令: {' '.join(gs_cmd)}")

            # 执行转换
            result = subprocess.run(
                gs_cmd,
                capture_output=True,
                text=True,
                timeout=30,
                cwd=os.path.dirname(gs_path) if os.path.isabs(gs_path) else None
            )

            if result.returncode != 0:
                logger.error(f"Ghostscript转换失败 (返回码: {result.returncode})")
                logger.error(f"错误输出: {result.stderr}")
                return None

            # 读取转换结果
            if not os.path.exists(temp_output) or os.path.getsize(temp_output) == 0:
                logger.error("Ghostscript转换后未生成有效的PDF文件")
                return None

            with open(temp_output, 'rb') as f:
                converted_data = f.read()

            # 验证转换结果
            if not converted_data.startswith(b'%PDF'):
                logger.error("Ghostscript转换结果不是有效的PDF文件")
                return None

            logger.info(f"Ghostscript转换成功: {len(pclm_data)} -> {len(converted_data)} 字节")
            return converted_data

        except subprocess.TimeoutExpired:
            logger.error("Ghostscript转换超时")
            return None
        except Exception as e:
            logger.error(f"Ghostscript转换过程中出错: {e}")
            return None
        finally:
            # 清理临时文件
            for temp_file in [temp_input, temp_output]:
                if temp_file and os.path.exists(temp_file):
                    try:
                        os.unlink(temp_file)
                    except Exception as e:
                        logger.warning(f"清理临时文件失败 {temp_file}: {e}")

    def _is_xps_format(self, data: bytes) -> bool:
        """检测XPS格式"""
        try:
            # XPS是基于ZIP的XML格式
            if data.startswith(b'PK\x03\x04'):
                # 检查是否包含XPS特征文件
                sample = data[:8192].lower()
                xps_indicators = [
                    b'_rels/.rels',
                    b'[content_types].xml',
                    b'fixeddocument',
                    b'fixedpage',
                ]
                return any(indicator in sample for indicator in xps_indicators)
            return False
        except Exception:
            return False

    def _is_http_wrapped_pclm(self, data: bytes) -> bool:
        """检测HTTP/SOAP包装的PCLM数据"""
        try:
            # 检查是否包含HTTP头和PCLM特征
            data_str = data.decode('utf-8', errors='ignore').lower()

            # WSD协议特征
            wsd_indicators = [
                'application/soap+xml',
                'senddocument',
                'wsdapi',
                'multipart/related'
            ]

            # PCLM数据特征
            pclm_indicators = [
                '%pdf',
                '%pclm',
                'genpclm',
                'strip-height'
            ]

            # 必须同时包含WSD协议特征和PCLM数据特征
            has_wsd = any(indicator in data_str for indicator in wsd_indicators)
            has_pclm = any(indicator in data_str for indicator in pclm_indicators)

            return has_wsd and has_pclm

        except Exception:
            return False

    def _extract_pclm_from_http(self, data: bytes) -> bytes:
        """从HTTP/SOAP包装中提取PCLM数据"""
        try:
            # 查找PDF标记的位置
            pdf_start = data.find(b'%PDF')
            if pdf_start == -1:
                logger.warning("在HTTP数据中未找到PDF标记")
                return data

            # 从PDF标记开始提取数据
            pclm_data = data[pdf_start:]

            # 查找数据结束位置（通常是MIME边界或HTTP结束）
            # 查找%%EOF标记
            eof_pos = pclm_data.rfind(b'%%EOF')
            if eof_pos != -1:
                pclm_data = pclm_data[:eof_pos + 5]
            else:
                # 如果没有找到%%EOF，查找MIME边界
                boundary_patterns = [
                    b'--4960e931-',  # 常见的MIME边界
                    b'\r\n--',       # 通用MIME边界
                    b'\n--',         # 通用MIME边界
                ]

                for pattern in boundary_patterns:
                    boundary_pos = pclm_data.find(pattern, 100)  # 跳过前100字节避免误匹配
                    if boundary_pos != -1:
                        pclm_data = pclm_data[:boundary_pos]
                        break

            logger.info(f"从HTTP包装中提取PCLM数据: {len(data)} -> {len(pclm_data)} 字节")
            return pclm_data

        except Exception as e:
            logger.error(f"提取PCLM数据时出错: {e}")
            return data

    def process_pclm_data(self, data: bytes) -> bytes:
        """使用Ghostscript转换PCLm数据为PDF"""
        try:
            logger.info("开始使用Ghostscript进行PCLm处理...")

            # 如果是HTTP包装的PCLM数据，先提取纯PCLM数据
            if self._is_http_wrapped_pclm(data):
                logger.info("检测到HTTP包装的PCLM数据，正在提取...")
                data = self._extract_pclm_from_http(data)

            # 使用Ghostscript转换
            converted_data = self._convert_pclm_with_ghostscript(data)
            if converted_data:
                logger.info("Ghostscript转换成功")
                return converted_data
            else:
                logger.error("Ghostscript转换失败")
                return data

        except Exception as e:
            logger.error(f"PCLm处理时出错: {e}")
            logger.info("返回原始数据以确保安全")
            return data

    def generate_filename(self, format_type: str, client_addr: str) -> str:
        """生成带时间戳的文件名"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        client_ip = client_addr.replace(':', '_').replace('.', '_')

        extensions = {
            # PDF相关
            'pdf': 'pdf',
            'pclm': 'pdf',

            # PostScript相关
            'postscript': 'ps',

            # PCL相关
            'pcl': 'pcl',
            'pjl': 'pjl',
            'escp': 'prn',

            # Windows格式
            'xps': 'xps',
            'emf': 'emf',
            'wmf': 'wmf',

            # 图像格式
            'png': 'png',
            'jpeg': 'jpg',
            'gif': 'gif',
            'bmp': 'bmp',

            # 其他
            'compressed': 'bin',
            'zip': 'zip',
            'gzip': 'gz',
            'text': 'txt',
            'raw': 'prn',
            'unknown': 'dat',
            'riff': 'riff'
        }

        ext = extensions.get(format_type, 'dat')
        return f"print_{timestamp}_{client_ip}.{ext}"

    def save_data(self, data: bytes, client_id: str) -> tuple:
        """保存数据到文件"""
        if not data:
            raise ValueError("数据为空")

        format_type = self.detect_format(data)
        filename = self.generate_filename(format_type, client_id)
        filepath = self.output_dir / filename

        # 根据格式处理数据
        processed_data = data
        if format_type == 'pclm':
            processed_data = self.process_pclm_data(data)
            # 如果处理后的数据是PDF，更新文件名为PDF格式
            if processed_data.startswith(b'%PDF'):
                # 将原来的扩展名替换为.pdf
                filename_base = filename.rsplit('.', 1)[0]
                filename = f"{filename_base}_converted.pdf"
                filepath = self.output_dir / filename
                logger.info(f"PCLM转换为PDF，文件名更新为: {filename}")

        # 保存文件
        with open(filepath, 'wb') as f:
            f.write(processed_data)

        file_size = len(processed_data)
        logger.info(f"文件保存成功: {filepath} (大小: {file_size} 字节)")

        return str(filepath), file_size

class PrintSniffer:
    """增强的打印流量嗅探器 - 支持完整Windows打印协议"""

    def __init__(self, target_ports=None, enable_wsd=True, max_stream_size=100*1024*1024):
        # 完整的Windows打印协议端口列表
        if target_ports is None:
            self.target_ports = [
                # 标准打印协议
                9100,  # AppSocket/JetDirect (HP标准)
                515,   # LPD (Line Printer Daemon)
                631,   # IPP (Internet Printing Protocol)

                # Windows特定协议
                445,   # SMB/CIFS 打印共享
                139,   # NetBIOS SMB

                # 厂商特定端口
                3910,  # 某些HP打印机
                9220,  # 某些Canon打印机
                9500,  # 某些Epson打印机
                9600,  # 某些Brother打印机
                8080,  # 某些网络打印机Web接口
                8443,  # HTTPS Web接口

                # 其他常见端口
                35,    # 某些老式打印机
                2000,  # 某些Xerox打印机
                4010,  # 某些Ricoh打印机
                9101,  # 备用AppSocket端口
                9102,  # 备用AppSocket端口
            ]
        else:
            self.target_ports = target_ports

        self.enable_wsd = enable_wsd
        self.max_stream_size = max_stream_size  # 最大流大小限制
        self.processor = EnhancedPrintDataProcessor()  # 使用增强处理器
        self.tcp_streams = defaultdict(lambda: {
            'packets': [],  # 存储带序列号的数据包
            'start_time': time.time(),
            'last_activity': time.time(),
            'base_seq': None,  # 基础序列号
            'expected_seq': None  # 期望的下一个序列号
        })
        self.running = False
        
    def is_print_packet(self, packet):
        """判断是否为打印相关的数据包"""
        if not packet.haslayer(TCP):
            return False
            
        tcp_layer = packet[TCP]
        return (tcp_layer.dport in self.target_ports or 
                tcp_layer.sport in self.target_ports)
    
    def get_stream_key(self, packet):
        """获取TCP流的唯一标识"""
        # 支持IPv4和IPv6
        if packet.haslayer(IP):
            ip_layer = packet[IP]
            src_ip = ip_layer.src
            dst_ip = ip_layer.dst
        elif packet.haslayer(IPv6):
            ip_layer = packet[IPv6]
            src_ip = ip_layer.src
            dst_ip = ip_layer.dst
        else:
            return None

        tcp_layer = packet[TCP]

        # 使用源IP:端口->目标IP:端口作为流标识
        return f"{src_ip}:{tcp_layer.sport}->{dst_ip}:{tcp_layer.dport}"
    
    def process_packet(self, packet):
        """处理捕获的数据包"""
        try:
            if not self.is_print_packet(packet):
                return

            # 获取流标识
            stream_key = self.get_stream_key(packet)
            if not stream_key:
                return  # 无法获取流标识，跳过

            tcp_layer = packet[TCP]

            # 检查是否有数据载荷
            if packet.haslayer(Raw):
                payload = packet[Raw].load

                if payload:
                    # 添加到TCP流（带序列号排序）
                    stream = self.tcp_streams[stream_key]
                    stream['last_activity'] = time.time()

                    # 初始化基础序列号
                    if stream['base_seq'] is None:
                        stream['base_seq'] = tcp_layer.seq
                        stream['expected_seq'] = tcp_layer.seq

                    # 存储数据包信息（用于后续排序）
                    packet_info = {
                        'seq': tcp_layer.seq,
                        'ack': tcp_layer.ack,
                        'flags': tcp_layer.flags,
                        'payload': payload,
                        'data_len': len(payload),
                        'timestamp': time.time(),
                        'relative_seq': tcp_layer.seq - stream['base_seq']
                    }

                    stream['packets'].append(packet_info)

                    # 确定IP版本
                    ip_version = "IPv6" if packet.haslayer(IPv6) else "IPv4"

                    logger.info(f"捕获打印数据 ({ip_version}): {stream_key} "
                              f"数据长度: {len(payload)} 字节 "
                              f"TCP标志: {tcp_layer.flags} "
                              f"序列号: {tcp_layer.seq}")

            # 检查数据完整性，如果数据完整则立即处理
            if self._is_data_complete(stream_key):
                logger.info(f"检测到完整数据，立即处理: {stream_key}")
                self.finalize_stream(stream_key)

            # 检查连接结束
            elif tcp_layer.flags & 0x01:  # FIN标志
                logger.info(f"TCP连接结束，处理剩余数据: {stream_key}")
                self.finalize_stream(stream_key)

            elif tcp_layer.flags & 0x04:  # RST标志
                logger.info(f"TCP连接重置: {stream_key}")
                self.finalize_stream(stream_key)

        except Exception as e:
            logger.error(f"处理数据包时出错: {e}")
            # 添加更详细的错误信息
            if hasattr(packet, 'summary'):
                logger.debug(f"问题数据包摘要: {packet.summary()}")

    def _is_data_complete(self, stream_key) -> bool:
        """检测数据是否完整，可以立即处理"""
        if stream_key not in self.tcp_streams:
            return False

        stream = self.tcp_streams[stream_key]
        packets = stream['packets']

        if not packets:
            return False

        try:
            # 重组当前数据以检查完整性
            sorted_packets = sorted(packets, key=lambda p: p['seq'])
            combined_data = b''.join([p['payload'] for p in sorted_packets])

            if not combined_data:
                return False

            # 检查PDF/PCLM完整性标记
            if b'%PDF' in combined_data:
                # 检查PDF结束标记
                if b'%%EOF' in combined_data:
                    logger.info(f"检测到完整的PDF数据: {stream_key}")
                    return True

            # 检查HTTP传输完整性
            if b'HTTP/' in combined_data and b'Content-Length:' in combined_data:
                # 提取Content-Length
                try:
                    data_str = combined_data.decode('utf-8', errors='ignore')
                    import re
                    content_length_match = re.search(r'Content-Length:\s*(\d+)', data_str, re.IGNORECASE)
                    if content_length_match:
                        expected_length = int(content_length_match.group(1))
                        # 查找HTTP头结束位置
                        header_end = combined_data.find(b'\r\n\r\n')
                        if header_end == -1:
                            header_end = combined_data.find(b'\n\n')  # 尝试\n\n格式
                        if header_end != -1:
                            body_start = header_end + (4 if b'\r\n\r\n' in combined_data else 2)
                            body_data = combined_data[body_start:]
                            if len(body_data) >= expected_length:
                                logger.info(f"检测到完整的HTTP数据: {stream_key} (期望:{expected_length}, 实际:{len(body_data)})")
                                return True
                except Exception as e:
                    logger.debug(f"HTTP Content-Length检测出错: {e}")
                    pass

            # 检查chunked传输完整性
            if b'Transfer-Encoding: chunked' in combined_data:
                # 查找chunked传输结束标记 (0\r\n\r\n)
                if b'0\r\n\r\n' in combined_data:
                    logger.info(f"检测到完整的chunked传输数据: {stream_key}")
                    return True

            # 检查SOAP/XML完整性
            if b'</soap:Envelope>' in combined_data and b'<soap:Envelope' in combined_data:
                logger.info(f"检测到完整的SOAP数据: {stream_key}")
                return True

            return False

        except Exception as e:
            logger.debug(f"检查数据完整性时出错: {e}")
            return False

    def finalize_stream(self, stream_key):
        """完成TCP流并保存数据 - 包含数据包排序和重复检测"""
        if stream_key not in self.tcp_streams:
            logger.debug(f"TCP流已被处理: {stream_key}")
            return

        stream = self.tcp_streams[stream_key]
        packets = stream['packets']

        if not packets:
            del self.tcp_streams[stream_key]
            return

        try:
            logger.info(f"开始重组TCP流: {stream_key}")
            logger.info(f"收到 {len(packets)} 个数据包，开始排序和去重...")

            # 1. 按序列号排序数据包
            sorted_packets = sorted(packets, key=lambda p: p['seq'])
            logger.info(f"数据包排序完成")

            # 2. 智能去重和间隙检测
            unique_packets = []
            covered_ranges = []

            for packet in sorted_packets:
                seq_start = packet['seq']
                seq_end = seq_start + packet['data_len']

                # 检查是否完全重复（完全相同的序列号范围）
                is_exact_duplicate = False
                for start, end in covered_ranges:
                    if seq_start == start and seq_end == end:
                        is_exact_duplicate = True
                        logger.debug(f"检测到完全重复数据包: seq={seq_start}-{seq_end}")
                        break

                # 检查是否部分重叠（但不是完全重复）
                has_overlap = False
                overlap_info = []
                for start, end in covered_ranges:
                    if (seq_start < end and seq_end > start):  # 有重叠
                        has_overlap = True
                        overlap_info.append((start, end))

                if is_exact_duplicate:
                    # 完全重复，跳过
                    continue
                elif has_overlap:
                    # 部分重叠，检查是否填补间隙
                    fills_gap = False
                    for start, end in overlap_info:
                        # 如果这个数据包填补了间隙，保留它
                        if seq_start < start or seq_end > end:
                            fills_gap = True
                            logger.debug(f"数据包填补间隙: seq={seq_start}-{seq_end}, 已有={start}-{end}")
                            break

                    if fills_gap:
                        unique_packets.append(packet)
                        covered_ranges.append((seq_start, seq_end))
                    else:
                        logger.debug(f"跳过重叠数据包: seq={seq_start}-{seq_end}")
                else:
                    # 无重叠，直接添加
                    unique_packets.append(packet)
                    covered_ranges.append((seq_start, seq_end))

            logger.info(f"智能去重完成: {len(packets)} -> {len(unique_packets)} 个数据包")

            # 3. 检测数据间隙
            if len(unique_packets) > 1:
                gaps = []
                for i in range(len(unique_packets) - 1):
                    current_end = unique_packets[i]['seq'] + unique_packets[i]['data_len']
                    next_start = unique_packets[i + 1]['seq']

                    if next_start > current_end:
                        gap_size = next_start - current_end
                        gaps.append((current_end, next_start, gap_size))
                        logger.warning(f"检测到数据间隙: {current_end}-{next_start} (大小: {gap_size} 字节)")

                if gaps:
                    logger.warning(f"总共发现 {len(gaps)} 个数据间隙，可能导致内容撕裂")
                else:
                    logger.info("未发现数据间隙，TCP流完整")

            # 4. 重新组装数据（按序列号顺序）
            data = io.BytesIO()
            total_bytes = 0

            # 再次按序列号排序，确保正确顺序
            final_sorted_packets = sorted(unique_packets, key=lambda p: p['seq'])

            for i, packet in enumerate(final_sorted_packets):
                data.write(packet['payload'])
                total_bytes += packet['data_len']

                # 记录重组进度
                if i == 0:
                    logger.debug(f"开始重组: seq={packet['seq']}, len={packet['data_len']}")
                elif i == len(final_sorted_packets) - 1:
                    logger.debug(f"结束重组: seq={packet['seq']}, len={packet['data_len']}")

            final_data = data.getvalue()
            logger.info(f"TCP流重组完成: {total_bytes} 字节，数据完整性: {'完整' if not gaps else '有间隙'}")

            # 4. 保存数据
            if final_data:
                # 从流标识中提取信息
                parts = stream_key.replace('->', '_to_').replace(':', '_')
                client_id = parts

                filepath, file_size = self.processor.save_data(final_data, client_id)

                duration = time.time() - stream['start_time']
                logger.info(f"""
打印流量捕获完成:
- TCP流: {stream_key}
- 数据大小: {len(final_data)} 字节
- 保存文件: {filepath}
- 数据包数量: {len(unique_packets)}
- 持续时间: {duration:.2f} 秒
                """.strip())

        except Exception as e:
            logger.error(f"重组TCP流时出错: {e}")
            logger.error(f"流信息: {len(packets)} 个数据包")

        # 清理流数据（线程安全）
        if stream_key in self.tcp_streams:
            del self.tcp_streams[stream_key]
            logger.debug(f"TCP流清理完成: {stream_key}")
        else:
            logger.debug(f"TCP流已被清理: {stream_key}")
    
    def cleanup_old_streams(self):
        """清理超时的TCP流"""
        current_time = time.time()
        timeout = 300  # 5分钟超时
        expired_streams = []
        
        for stream_key, stream in self.tcp_streams.items():
            if current_time - stream['last_activity'] > timeout:
                expired_streams.append(stream_key)
        
        for stream_key in expired_streams:
            logger.info(f"清理超时流: {stream_key}")
            self.finalize_stream(stream_key)
    
    def cleanup_worker(self):
        """清理工作线程"""
        while self.running:
            time.sleep(60)  # 每分钟清理一次
            self.cleanup_old_streams()
    
    def start_sniffing(self, interface=None):
        """开始嗅探网络流量"""
        if not SCAPY_AVAILABLE:
            logger.error("Scapy库未安装，无法启动嗅探")
            return False
        
        self.running = True
        
        # 启动清理线程
        cleanup_thread = threading.Thread(target=self.cleanup_worker, daemon=True)
        cleanup_thread.start()
        
        # 构建过滤器，支持IPv4和IPv6
        if not self.target_ports:
            logger.error("未设置目标端口，无法启动嗅探")
            return False

        port_filter = " or ".join([f"port {port}" for port in self.target_ports])
        filter_str = f"tcp and ({port_filter})"
        
        logger.info(f"开始嗅探打印流量")
        logger.info(f"监听端口: {self.target_ports}")
        logger.info(f"过滤器: {filter_str}")
        if interface:
            logger.info(f"网络接口: {interface}")
        
        try:
            # 开始嗅探
            sniff(
                filter=filter_str,
                prn=self.process_packet,
                iface=interface,
                store=0,  # 不存储数据包
                stop_filter=lambda x: not self.running
            )
        except Exception as e:
            logger.error(f"嗅探过程中出错: {e}")
            return False
        
        return True
    
    def stop_sniffing(self):
        """停止嗅探"""
        self.running = False
        logger.info("正在停止嗅探...")
        
        # 保存所有未完成的流
        for stream_key in list(self.tcp_streams.keys()):
            self.finalize_stream(stream_key)

def list_interfaces():
    """列出可用的网络接口"""
    if not SCAPY_AVAILABLE:
        return []
    
    try:
        from scapy.all import get_if_list, get_if_addr
        interfaces = []
        
        for iface in get_if_list():
            try:
                addr = get_if_addr(iface)
                interfaces.append((iface, addr))
            except:
                interfaces.append((iface, "未知"))
        
        return interfaces
    except Exception as e:
        logger.error(f"获取网络接口列表时出错: {e}")
        return []

def main():
    """主函数"""
    print("""
╔══════════════════════════════════════════════════════════════╗
║                    打印流量嗅探器                            ║
║                 Print Traffic Sniffer                       ║
║                                                              ║
║  功能: 监听本机发送到打印机的数据并还原为文件                  ║
║  依赖: pip install scapy                                    ║
║  Linux: 可能需要root权限运行                                ║
╚══════════════════════════════════════════════════════════════╝
    """)
    
    if not SCAPY_AVAILABLE:
        print("❌ 请先安装依赖:")
        print("   pip install scapy")

        # 平台特定的安装提示
        current_platform = platform.system().lower()
        if current_platform == "windows":
            print("   Windows用户还需要安装Npcap: https://nmap.org/npcap/")
        elif current_platform == "linux":
            print("   Linux用户可能需要安装libpcap-dev和以root权限运行:")
            print("   sudo apt-get install libpcap-dev  # Ubuntu/Debian")
            print("   sudo yum install libpcap-devel    # CentOS/RHEL")
        elif current_platform == "darwin":
            print("   macOS用户可能需要安装libpcap: brew install libpcap")

        input("按任意键退出...")
        return

    # Linux权限检查
    if platform.system().lower() == "linux" and os.geteuid() != 0:
        print("⚠️  警告: 在Linux上运行网络嗅探通常需要root权限")
        print("   建议使用: sudo python3 print_sniffer_pcap.py")
        choice = input("是否继续尝试运行? (y/N): ").lower()
        if choice not in ['y', 'yes']:
            return

    # 列出网络接口
    print("📡 可用的网络接口:")
    interfaces = list_interfaces()

    # 确保interfaces不为None且为列表
    if not interfaces:
        interfaces = []
        print("  未找到可用的网络接口，将使用默认接口")
    else:
        for i, (iface, addr) in enumerate(interfaces):
            print(f"  {i+1}. {iface} ({addr})")

    print()
    print("选择网络接口 (直接回车使用默认接口):")
    choice = input("请输入接口编号: ").strip()

    selected_interface = None
    if choice.isdigit() and interfaces:
        idx = int(choice) - 1
        if 0 <= idx < len(interfaces):
            selected_interface = interfaces[idx][0]
            print(f"✅ 选择接口: {selected_interface}")

    if not selected_interface and interfaces:
        print("使用默认接口")
    
    print()
    print("🚀 开始嗅探打印流量...")
    print("📁 捕获的文件将保存到 captured_prints 目录")
    print("⏹️  按 Ctrl+C 停止嗅探")
    print()
    
    sniffer = PrintSniffer()
    
    try:
        sniffer.start_sniffing(interface=selected_interface)
    except KeyboardInterrupt:
        print("\n用户中断，正在停止...")
    except Exception as e:
        logger.error(f"程序运行时出错: {e}")
    finally:
        sniffer.stop_sniffing()
        print("程序已退出")

if __name__ == "__main__":
    main()
