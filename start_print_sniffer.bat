@echo off
chcp 65001 >nul
title Print Traffic Sniffer

echo ========================================
echo        Print Traffic Sniffer
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if errorlevel 1 (
    echo Error: This program requires Administrator privileges
    echo Please right-click and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

echo Running with Administrator privileges ✓
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    pause
    exit /b 1
)

REM Check if required files exist
if not exist "print_sniffer_pcap.py" (
    echo Error: print_sniffer_pcap.py not found
    pause
    exit /b 1
)

if not exist "print_capture_server.py" (
    echo Error: print_capture_server.py not found
    pause
    exit /b 1
)

echo Starting Enhanced Print Traffic Sniffer...
echo.
python print_sniffer_pcap.py

echo.
echo Program finished.
pause
