# 打印流量嗅探器 - Linux使用指南

## 📋 系统要求

- **操作系统**: Linux (Ubuntu, Debian, CentOS, RHEL, Fedora等)
- **Python版本**: Python 3.6+
- **权限**: root权限（用于网络嗅探）
- **网络**: 支持IPv4/IPv6

## 🚀 安装步骤

### 1. 安装系统依赖

#### Ubuntu/Debian系统:
```bash
# 更新包管理器
sudo apt update

# 安装libpcap开发库
sudo apt-get install libpcap-dev python3-dev

# 安装Python包管理器（如果没有）
sudo apt-get install python3-pip
```

#### CentOS/RHEL/Fedora系统:
```bash
# CentOS/RHEL
sudo yum install libpcap-devel python3-devel python3-pip

# Fedora
sudo dnf install libpcap-devel python3-devel python3-pip
```

#### Arch Linux:
```bash
sudo pacman -S libpcap python-pip
```

### 2. 安装Python依赖

```bash
# 安装scapy库
pip3 install scapy

# 或者使用系统包管理器
sudo apt-get install python3-scapy  # Ubuntu/Debian
sudo yum install python3-scapy      # CentOS/RHEL
```

### 3. 下载程序

```bash
# 下载程序文件
wget https://your-server.com/print_sniffer_pcap.py
# 或者从Git仓库克隆

# 给予执行权限
chmod +x print_sniffer_pcap.py
```

## 🔧 运行程序

### 基本运行

```bash
# 使用root权限运行
sudo python3 print_sniffer_pcap.py

# 或者使用sudo -E保持环境变量
sudo -E python3 print_sniffer_pcap.py
```

### 高级运行选项

```bash
# 指定网络接口
sudo python3 print_sniffer_pcap.py --interface eth0

# 后台运行
sudo nohup python3 print_sniffer_pcap.py > sniffer.log 2>&1 &

# 使用screen在后台运行
screen -S print_sniffer
sudo python3 print_sniffer_pcap.py
# Ctrl+A, D 分离会话
```

## 🌐 网络接口选择

### 查看可用接口
```bash
# 查看所有网络接口
ip addr show

# 查看活动接口
ip link show up

# 使用ifconfig（如果安装了net-tools）
ifconfig
```

### 常见接口名称
- **以太网**: `eth0`, `ens33`, `enp0s3`
- **无线网络**: `wlan0`, `wlp2s0`
- **虚拟接口**: `docker0`, `br-*`
- **回环接口**: `lo`

## 🔒 权限和安全

### 为什么需要root权限？
- 网络数据包捕获需要访问原始套接字
- Linux内核限制普通用户访问网络接口的底层功能

### 安全运行建议
```bash
# 1. 创建专用用户（可选）
sudo useradd -r -s /bin/false print_sniffer

# 2. 使用capabilities而不是完整root权限
sudo setcap cap_net_raw,cap_net_admin=eip /usr/bin/python3
python3 print_sniffer_pcap.py

# 3. 限制文件权限
chmod 700 print_sniffer_pcap.py
```

## 📁 文件输出

### 默认输出目录
```bash
./captured_prints/
├── print_20250725_120000_192.168.1.100_to_192.168.1.200.pdf
├── print_20250725_120001_enhanced.pdf
└── print_sniffer.log
```

### 自定义输出目录
```bash
# 修改程序中的output_dir参数
# 或者创建符号链接
ln -s /var/log/print_captures ./captured_prints
```

## 🐛 故障排除

### 常见问题

#### 1. 权限被拒绝
```bash
# 错误: Operation not permitted
# 解决: 使用sudo运行
sudo python3 print_sniffer_pcap.py
```

#### 2. 找不到网络接口
```bash
# 错误: No such device
# 解决: 检查接口名称
ip addr show
```

#### 3. scapy导入失败
```bash
# 错误: ModuleNotFoundError: No module named 'scapy'
# 解决: 安装scapy
pip3 install scapy
```

#### 4. libpcap错误
```bash
# 错误: OSError: libpcap not found
# 解决: 安装libpcap开发库
sudo apt-get install libpcap-dev  # Ubuntu/Debian
sudo yum install libpcap-devel    # CentOS/RHEL
```

### 调试模式

```bash
# 启用详细日志
export PYTHONPATH=/usr/local/lib/python3.x/site-packages
sudo -E python3 -v print_sniffer_pcap.py

# 检查scapy配置
python3 -c "from scapy.all import *; conf"
```

## 🔧 系统服务配置

### 创建systemd服务

```bash
# 创建服务文件
sudo nano /etc/systemd/system/print-sniffer.service
```

```ini
[Unit]
Description=Print Traffic Sniffer
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/opt/print-sniffer
ExecStart=/usr/bin/python3 /opt/print-sniffer/print_sniffer_pcap.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

```bash
# 启用和启动服务
sudo systemctl daemon-reload
sudo systemctl enable print-sniffer
sudo systemctl start print-sniffer

# 查看状态
sudo systemctl status print-sniffer
```

## 📊 性能优化

### 系统调优
```bash
# 增加网络缓冲区大小
echo 'net.core.rmem_max = 134217728' >> /etc/sysctl.conf
echo 'net.core.rmem_default = 134217728' >> /etc/sysctl.conf
sudo sysctl -p

# 禁用不必要的网络服务
sudo systemctl disable NetworkManager-wait-online
```

### 监控资源使用
```bash
# 监控CPU和内存使用
top -p $(pgrep -f print_sniffer_pcap.py)

# 监控网络流量
sudo iftop -i eth0
```

## 🔗 相关链接

- [Scapy官方文档](https://scapy.readthedocs.io/)
- [libpcap文档](https://www.tcpdump.org/manpages/pcap.3pcap.html)
- [Linux网络命名空间](https://man7.org/linux/man-pages/man7/network_namespaces.7.html)

## 📞 技术支持

如果遇到问题，请提供以下信息：
- Linux发行版和版本
- Python版本
- 错误日志
- 网络配置信息
